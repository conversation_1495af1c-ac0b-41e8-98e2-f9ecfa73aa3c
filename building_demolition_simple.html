<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D建筑爆破演示（简化版）</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background: #000;
        }
        
        #canvas-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        #controls h3 {
            margin-top: 0;
            color: #ff6b6b;
            text-align: center;
            font-size: 18px;
        }
        
        .control-group {
            margin: 15px 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #ddd;
        }
        
        select, button {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        select {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        button {
            background: #4CAF50;
            color: white;
            font-weight: bold;
            margin-top: 5px;
        }
        
        button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        button.danger {
            background: #ff4444;
        }
        
        button.danger:hover {
            background: #cc0000;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
        }
        
        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <div id="loading">正在加载...</div>
        <div id="status">状态：初始化中...</div>
        <div id="controls">
            <h3>🏗️ 建筑爆破控制面板</h3>
            
            <div class="control-group">
                <label>爆破模式：</label>
                <select id="blastMode">
                    <option value="controlled">控制爆破（定向倒塌）</option>
                    <option value="sequential">顺序爆破（从下到上）</option>
                    <option value="simultaneous">同步爆破（多点同时）</option>
                </select>
            </div>
            
            <div class="control-group">
                <button id="previewBtn">预览爆破点</button>
                <button id="resetBtn">重置建筑</button>
                <button id="detonateBtn" class="danger">💥 执行爆破</button>
            </div>
            
            <div class="control-group">
                <label>相机视角：</label>
                <button id="cameraView1">正面视角</button>
                <button id="cameraView2">俯视视角</button>
            </div>
        </div>
        
        <div id="info">
            鼠标左键：旋转视角 | 鼠标右键：平移 | 滚轮：缩放
        </div>
    </div>

    <!-- 使用CDN加载Three.js -->
    <script src="https://unpkg.com/three@0.169.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.169.0/examples/js/controls/OrbitControls.js"></script>

    <script>
        // 状态更新函数
        function updateStatus(message) {
            console.log(message);
            document.getElementById('status').textContent = '状态：' + message;
        }

        // 全局变量
        let scene, camera, renderer, controls;
        let building = [];
        let blastPoints = [];
        let isExploding = false;
        
        // 建筑参数
        const BUILDING_PARAMS = {
            radius: 10,
            bricksPerLayer: 16,
            layers: 40,
            brickWidth: 2,
            brickHeight: 1,
            brickDepth: 1
        };

        // 检查Three.js是否加载成功
        function checkThreeJS() {
            if (typeof THREE === 'undefined') {
                updateStatus('Three.js加载失败');
                document.getElementById('loading').innerHTML = '加载失败：Three.js库无法加载';
                return false;
            }
            updateStatus('Three.js加载成功');
            return true;
        }

        // 初始化Three.js场景
        function initThree() {
            try {
                updateStatus('初始化Three.js场景...');
                
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB);
                scene.fog = new THREE.Fog(0x87CEEB, 50, 200);

                // 相机
                camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                camera.position.set(40, 30, 40);
                camera.lookAt(0, 20, 0);

                // 渲染器
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setPixelRatio(window.devicePixelRatio);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;

                document.getElementById('canvas-container').appendChild(renderer.domElement);

                // 控制器
                controls = new THREE.OrbitControls(camera, renderer.domElement);
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.target.set(0, 20, 0);
                controls.update();

                updateStatus('Three.js场景初始化完成');
                return true;
            } catch (error) {
                updateStatus('Three.js场景初始化失败: ' + error.message);
                console.error('Three.js场景初始化失败:', error);
                return false;
            }
        }

        // 设置灯光
        function setupLighting() {
            updateStatus('设置灯光...');
            
            // 环境光
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            // 主方向光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -50;
            directionalLight.shadow.camera.right = 50;
            directionalLight.shadow.camera.top = 50;
            directionalLight.shadow.camera.bottom = -50;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 200;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            updateStatus('灯光设置完成');
        }

        // 创建地面
        function createGround() {
            updateStatus('创建地面...');

            const groundGeometry = new THREE.PlaneGeometry(80, 80);
            const groundMaterial = new THREE.MeshStandardMaterial({
                color: 0x404040,
                roughness: 0.8,
                metalness: 0.2
            });

            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);

            updateStatus('地面创建完成');
        }

        // 创建砖块
        function createBrick(x, y, z, rotation) {
            const geometry = new THREE.BoxGeometry(
                BUILDING_PARAMS.brickWidth,
                BUILDING_PARAMS.brickHeight,
                BUILDING_PARAMS.brickDepth
            );

            const brickMaterial = new THREE.MeshStandardMaterial({
                color: 0xD2B48C, // 米色
                roughness: 0.7,
                metalness: 0.1
            });

            const mesh = new THREE.Mesh(geometry, brickMaterial);
            mesh.position.set(x, y, z);
            mesh.rotation.y = rotation;
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            scene.add(mesh);

            // 简化物理体
            const body = {
                position: { x, y, z },
                velocity: { x: 0, y: 0, z: 0 },
                angularVelocity: { x: 0, y: 0, z: 0 },
                active: false,
                mass: 5
            };

            return { mesh, body };
        }

        // 创建圆形建筑
        function createBuilding() {
            updateStatus('创建建筑...');
            building = [];

            for (let layer = 0; layer < BUILDING_PARAMS.layers; layer++) {
                const angleStep = (Math.PI * 2) / BUILDING_PARAMS.bricksPerLayer;

                for (let i = 0; i < BUILDING_PARAMS.bricksPerLayer; i++) {
                    let angle = i * angleStep;

                    // 偶数层偏移50%
                    if (layer % 2 === 0) {
                        angle += angleStep * 0.5;
                    }

                    const adjustedRadius = BUILDING_PARAMS.radius - BUILDING_PARAMS.brickDepth * 0.45;

                    const x = Math.cos(angle) * adjustedRadius;
                    const y = layer * BUILDING_PARAMS.brickHeight + BUILDING_PARAMS.brickHeight / 2;
                    const z = Math.sin(angle) * adjustedRadius;
                    const rotation = angle + Math.PI / 2;

                    const brick = createBrick(x, y, z, rotation);
                    building.push(brick);
                }
            }

            updateStatus(`建筑创建完成，共${building.length}块砖`);
        }

        // 配置爆破点
        function configureBlastPoints(mode) {
            blastPoints = [];

            switch(mode) {
                case 'controlled':
                    // 底层关键支撑点
                    for (let i = 0; i < 4; i++) {
                        const angle = (Math.PI * 2 / 4) * i;
                        blastPoints.push({
                            position: new THREE.Vector3(
                                Math.cos(angle) * BUILDING_PARAMS.radius,
                                1,
                                Math.sin(angle) * BUILDING_PARAMS.radius
                            ),
                            delay: i < 2 ? 0 : 100,
                            force: 800,
                            radius: 3
                        });
                    }
                    break;
                case 'sequential':
                    const layersToBlast = [0, 10, 20, 30];
                    layersToBlast.forEach((layer, index) => {
                        for (let i = 0; i < 4; i++) {
                            const angle = (Math.PI * 2 / 4) * i;
                            blastPoints.push({
                                position: new THREE.Vector3(
                                    Math.cos(angle) * BUILDING_PARAMS.radius,
                                    layer + 1,
                                    Math.sin(angle) * BUILDING_PARAMS.radius
                                ),
                                delay: index * 150,
                                force: 600,
                                radius: 2.5
                            });
                        }
                    });
                    break;
                case 'simultaneous':
                    const positions = [
                        { layer: 0, count: 8 },
                        { layer: 20, count: 6 }
                    ];
                    positions.forEach(pos => {
                        for (let i = 0; i < pos.count; i++) {
                            const angle = (Math.PI * 2 / pos.count) * i;
                            blastPoints.push({
                                position: new THREE.Vector3(
                                    Math.cos(angle) * BUILDING_PARAMS.radius,
                                    pos.layer + 1,
                                    Math.sin(angle) * BUILDING_PARAMS.radius
                                ),
                                delay: 0,
                                force: 700,
                                radius: 3
                            });
                        }
                    });
                    break;
            }
            updateStatus(`配置了${blastPoints.length}个爆破点`);
        }

        // 预览爆破点
        function previewBlastPoints() {
            // 清除之前的预览
            scene.children.filter(obj => obj.userData.isBlastMarker).forEach(marker => {
                scene.remove(marker);
            });

            blastPoints.forEach((blast, index) => {
                const marker = new THREE.Mesh(
                    new THREE.SphereGeometry(0.5, 16, 16),
                    new THREE.MeshBasicMaterial({
                        color: 0xff0000,
                        transparent: true,
                        opacity: 0.6
                    })
                );
                marker.position.copy(blast.position);
                marker.userData.isBlastMarker = true;
                scene.add(marker);

                // 3秒后移除
                setTimeout(() => {
                    scene.remove(marker);
                }, 3000);
            });
            updateStatus('爆破点预览已显示');
        }

        // 执行爆破
        async function executeBlasting() {
            if (isExploding) return;
            isExploding = true;
            updateStatus('开始爆破...');

            // 按延迟时间排序
            const sortedBlasts = [...blastPoints].sort((a, b) => a.delay - b.delay);

            for (const blast of sortedBlasts) {
                await new Promise(resolve => setTimeout(resolve, blast.delay));
                detonatePoint(blast);
            }

            updateStatus('爆破完成');
        }

        // 单点爆破
        function detonatePoint(blast) {
            building.forEach(brick => {
                const brickPos = brick.mesh.position;

                // 计算距离
                const dx = brickPos.x - blast.position.x;
                const dy = brickPos.y - blast.position.y;
                const dz = brickPos.z - blast.position.z;
                const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

                if (distance < blast.radius) {
                    // 激活物理体
                    brick.body.active = true;

                    // 计算爆炸力
                    const length = Math.sqrt(dx * dx + dz * dz);
                    const forceMagnitude = blast.force * (1 - distance / blast.radius);
                    const forceX = length > 0 ? (dx / length) * forceMagnitude : 0;
                    const forceY = blast.force * 0.3;
                    const forceZ = length > 0 ? (dz / length) * forceMagnitude : 0;

                    // 应用力
                    brick.body.velocity.x += forceX * 0.01;
                    brick.body.velocity.y += forceY * 0.01;
                    brick.body.velocity.z += forceZ * 0.01;

                    // 添加随机旋转
                    brick.body.angularVelocity.x += (Math.random() - 0.5) * 5;
                    brick.body.angularVelocity.y += (Math.random() - 0.5) * 5;
                    brick.body.angularVelocity.z += (Math.random() - 0.5) * 5;
                }
            });
        }

        // 重置场景
        function resetScene() {
            updateStatus('重置场景...');
            isExploding = false;

            // 清除旧建筑
            building.forEach(brick => {
                scene.remove(brick.mesh);
            });
            building = [];

            // 重新创建建筑
            createBuilding();
            updateStatus('场景重置完成');
        }

        // 设置UI事件
        function setupUI() {
            document.getElementById('previewBtn').addEventListener('click', () => {
                const mode = document.getElementById('blastMode').value;
                configureBlastPoints(mode);
                previewBlastPoints();
            });

            document.getElementById('resetBtn').addEventListener('click', () => {
                resetScene();
                document.getElementById('detonateBtn').disabled = false;
            });

            document.getElementById('detonateBtn').addEventListener('click', () => {
                if (!isExploding) {
                    const mode = document.getElementById('blastMode').value;
                    configureBlastPoints(mode);
                    executeBlasting();
                    document.getElementById('detonateBtn').disabled = true;
                }
            });

            document.getElementById('cameraView1').addEventListener('click', () => {
                camera.position.set(40, 30, 40);
                controls.target.set(0, 20, 0);
                controls.update();
            });

            document.getElementById('cameraView2').addEventListener('click', () => {
                camera.position.set(0, 80, 0);
                controls.target.set(0, 20, 0);
                controls.update();
            });
        }

        // 启动应用
        function init() {
            updateStatus('开始初始化应用...');

            // 检查Three.js
            if (!checkThreeJS()) {
                return;
            }

            // 初始化场景
            if (!initThree()) {
                return;
            }

            // 设置灯光
            setupLighting();

            // 创建地面
            createGround();

            // 创建建筑
            createBuilding();

            // 设置UI事件
            setupUI();

            // 移除加载提示
            document.getElementById('loading').style.display = 'none';
            updateStatus('应用初始化完成，准备就绪');

            // 开始动画循环
            animate();
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            // 更新物理模拟
            building.forEach(brick => {
                if (brick.body.active) {
                    // 重力
                    brick.body.velocity.y -= 0.5;

                    // 更新位置
                    brick.body.position.x += brick.body.velocity.x * 0.016;
                    brick.body.position.y += brick.body.velocity.y * 0.016;
                    brick.body.position.z += brick.body.velocity.z * 0.016;

                    // 地面碰撞检测
                    if (brick.body.position.y < 0.5) {
                        brick.body.position.y = 0.5;
                        brick.body.velocity.y *= -0.3; // 弹性碰撞
                        brick.body.velocity.x *= 0.8; // 摩擦
                        brick.body.velocity.z *= 0.8;
                    }

                    // 更新Three.js网格位置
                    brick.mesh.position.set(
                        brick.body.position.x,
                        brick.body.position.y,
                        brick.body.position.z
                    );

                    // 旋转动画
                    brick.mesh.rotation.x += brick.body.angularVelocity.x * 0.016;
                    brick.mesh.rotation.y += brick.body.angularVelocity.y * 0.016;
                    brick.mesh.rotation.z += brick.body.angularVelocity.z * 0.016;

                    // 速度衰减
                    brick.body.angularVelocity.x *= 0.99;
                    brick.body.angularVelocity.y *= 0.99;
                    brick.body.angularVelocity.z *= 0.99;
                }
            });

            // 更新控制器
            controls.update();

            // 渲染场景
            renderer.render(scene, camera);
        }

        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // 等待页面加载完成后初始化
        window.addEventListener('load', () => {
            setTimeout(init, 1000); // 延迟1秒确保所有资源加载完成
        });

        // 监听窗口大小变化
        window.addEventListener('resize', onWindowResize);
    </script>
</body>
</html>
