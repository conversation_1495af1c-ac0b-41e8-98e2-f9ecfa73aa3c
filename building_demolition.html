<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D建筑多点爆破拆除演示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background: #000;
        }
        
        #canvas-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        #controls h3 {
            margin-top: 0;
            color: #ff6b6b;
            text-align: center;
            font-size: 18px;
        }
        
        .control-group {
            margin: 15px 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #ddd;
        }
        
        select, button {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        select {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        button {
            background: #4CAF50;
            color: white;
            font-weight: bold;
            margin-top: 5px;
        }
        
        button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        button.danger {
            background: #ff4444;
        }
        
        button.danger:hover {
            background: #cc0000;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        input[type="range"] {
            flex: 1;
            -webkit-appearance: none;
            height: 5px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #ff6b6b;
            cursor: pointer;
        }
        
        .slider-value {
            min-width: 40px;
            text-align: right;
            color: #ff6b6b;
        }
        
        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
        }
        
        .blast-indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(255, 0, 0, 0.6);
            border: 2px solid #ff0000;
            pointer-events: none;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.5); opacity: 0.5; }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <div id="loading">加载中...</div>
        <div id="controls">
            <h3>🏗️ 建筑爆破控制面板</h3>
            
            <div class="control-group">
                <label>爆破模式：</label>
                <select id="blastMode">
                    <option value="controlled">控制爆破（定向倒塌）</option>
                    <option value="sequential">顺序爆破（从下到上）</option>
                    <option value="simultaneous">同步爆破（多点同时）</option>
                    <option value="implosion">内爆（向内坍塌）</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>爆破延迟倍率：</label>
                <div class="slider-container">
                    <input type="range" id="delayMultiplier" min="0.1" max="3" step="0.1" value="1">
                    <span class="slider-value">1.0x</span>
                </div>
            </div>
            
            <div class="control-group">
                <button id="previewBtn">预览爆破点</button>
                <button id="resetBtn">重置建筑</button>
                <button id="detonateBtn" class="danger">💥 执行爆破</button>
            </div>
            
            <div class="control-group">
                <label>相机视角：</label>
                <button id="cameraView1">正面视角</button>
                <button id="cameraView2">俯视视角</button>
                <button id="cameraView3">环绕视角</button>
            </div>
        </div>
        
        <div id="info">
            鼠标左键：旋转视角 | 鼠标右键：平移 | 滚轮：缩放
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.169.0/build/three.webgpu.js",
            "three/tsl": "https://unpkg.com/three@0.169.0/build/three.webgpu.js",
            "three/addons/": "https://unpkg.com/three@0.169.0/examples/jsm/",
            "@orillusion/core": "https://unpkg.com/@orillusion/core@latest/dist/orillusion.es.js",
            "@orillusion/physics": "https://unpkg.com/@orillusion/physics@latest/dist/physics.es.js"
        }
    }
    </script>

    <!-- 加载Ammo.js物理引擎 -->
    <script src="https://unpkg.com/ammo.js@0.0.10/builds/ammo.js" onerror="console.warn('Ammo.js加载失败，将使用简化物理模拟')"></script>

    <!-- 备用Three.js加载 -->
    <script>
        // 检测Three.js是否加载成功，如果失败则加载备用版本
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof THREE === 'undefined') {
                    console.warn('Three.js WebGPU版本加载失败，尝试加载WebGL版本');
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/three@0.169.0/build/three.min.js';
                    script.onload = () => {
                        console.log('Three.js WebGL版本加载成功');
                        // 加载OrbitControls
                        const controlsScript = document.createElement('script');
                        controlsScript.src = 'https://unpkg.com/three@0.169.0/examples/js/controls/OrbitControls.js';
                        document.head.appendChild(controlsScript);
                    };
                    script.onerror = () => {
                        console.error('Three.js完全加载失败');
                        document.getElementById('loading').innerHTML = '加载失败：Three.js库无法加载';
                    };
                    document.head.appendChild(script);
                }
            }, 2000);
        });
    </script>

    <script type="module">
        // 动态导入模块，添加错误处理
        let THREE, OrbitControls, WebGPU, Engine3D, Scene3D, Object3D, Vector3, Quaternion, Physics, Rigidbody;

        try {
            const threeModule = await import('three');
            THREE = threeModule;
            console.log('Three.js模块加载成功');

            const controlsModule = await import('three/addons/controls/OrbitControls.js');
            OrbitControls = controlsModule.OrbitControls;
            console.log('OrbitControls加载成功');

            const webgpuModule = await import('three/addons/capabilities/WebGPU.js');
            WebGPU = webgpuModule.default;
            console.log('WebGPU模块加载成功');

        } catch (error) {
            console.error('Three.js模块加载失败:', error);
            document.getElementById('loading').innerHTML = '加载失败：Three.js模块无法加载<br>错误：' + error.message;
            throw error;
        }

        // 尝试加载Orillusion模块（可选）
        try {
            const orillionCoreModule = await import('@orillusion/core');
            Engine3D = orillionCoreModule.Engine3D;
            Scene3D = orillionCoreModule.Scene3D;
            Object3D = orillionCoreModule.Object3D;
            Vector3 = orillionCoreModule.Vector3;
            Quaternion = orillionCoreModule.Quaternion;
            console.log('Orillusion核心模块加载成功');

            const orillionPhysicsModule = await import('@orillusion/physics');
            Physics = orillionPhysicsModule.Physics;
            Rigidbody = orillionPhysicsModule.Rigidbody;
            console.log('Orillusion物理模块加载成功');

        } catch (error) {
            console.warn('Orillusion模块加载失败，将使用简化物理模拟:', error);
            // 不抛出错误，继续使用简化物理模拟
        }

        // 检测WebGPU支持并决定使用哪种材质
        let useWebGPUMaterials = false;

        // 在渲染器初始化后检测
        function detectWebGPUSupport() {
            useWebGPUMaterials = renderer && renderer.backend && renderer.backend.isWebGPUBackend;
            console.log('使用WebGPU材质:', useWebGPUMaterials);
        }

        // 全局变量
        let scene, camera, renderer, controls;
        let engine3D; // Orillusion引擎
        let orillusion_scene; // Orillusion场景
        let physics; // Orillusion物理世界
        let building = [];
        let blastPoints = [];
        let particleSystems = [];
        let isExploding = false;
        let animationId;
        
        // 建筑参数
        const BUILDING_PARAMS = {
            radius: 10,
            bricksPerLayer: 20,
            layers: 60,
            brickWidth: 2,
            brickHeight: 1,
            brickDepth: 1
        };

        // 爆破模式
        const BlastingModes = {
            controlled: '控制爆破',
            sequential: '顺序爆破',
            simultaneous: '同步爆破',
            implosion: '内爆'
        };

        // 初始化Three.js场景
        async function initThree() {
            try {
                console.log('开始初始化Three.js场景...');
                document.getElementById('loading').innerHTML = '正在初始化Three.js...';

                // 检测 WebGPU 支持
                const webGPUAvailable = WebGPU.isAvailable();
                console.log('WebGPU 可用性:', webGPUAvailable);

                if (!webGPUAvailable) {
                    console.warn('WebGPU 不可用，将使用 WebGL 渲染器');
                    document.getElementById('loading').innerHTML = '正在加载... (使用 WebGL)';
                }

                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB); // 天空蓝
                scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
                console.log('场景创建完成');

                // 相机
                camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                camera.position.set(40, 30, 40);
                camera.lookAt(0, 20, 0);
                console.log('相机创建完成');

                // 尝试创建渲染器
                document.getElementById('loading').innerHTML = '正在初始化渲染器...';

                try {
                    // 优先尝试WebGPU渲染器
                    if (webGPUAvailable) {
                        renderer = new THREE.WebGPURenderer({
                            antialias: true,
                            powerPreference: 'high-performance'
                        });
                        console.log('WebGPU渲染器创建成功');
                    } else {
                        throw new Error('WebGPU不可用，切换到WebGL');
                    }
                } catch (webgpuError) {
                    console.warn('WebGPU渲染器创建失败，切换到WebGL:', webgpuError);
                    // 回退到WebGL渲染器
                    renderer = new THREE.WebGLRenderer({
                        antialias: true,
                        powerPreference: 'high-performance'
                    });
                    console.log('WebGL渲染器创建成功');
                }

                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setPixelRatio(window.devicePixelRatio);

                // 性能优化设置
                renderer.info.autoReset = false;
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;

                document.getElementById('canvas-container').appendChild(renderer.domElement);
                console.log('渲染器添加到DOM完成');

                // 渲染器初始化
                document.getElementById('loading').innerHTML = '正在初始化渲染器...';
                if (renderer.init) {
                    await renderer.init();
                    console.log('渲染器初始化成功');
                }

                // 检测WebGPU支持
                detectWebGPUSupport();

                // 控制器
                document.getElementById('loading').innerHTML = '正在设置控制器...';
                controls = new OrbitControls(camera, renderer.domElement);
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.target.set(0, 20, 0);
                controls.update();
                console.log('控制器设置完成');

                // 灯光
                document.getElementById('loading').innerHTML = '正在设置灯光...';
                setupLighting();
                console.log('灯光设置完成');

                // 地面
                document.getElementById('loading').innerHTML = '正在创建地面...';
                createGround();
                console.log('地面创建完成');

                // 初始化Orillusion物理世界
                document.getElementById('loading').innerHTML = '正在初始化物理引擎...';
                await initPhysics();
                console.log('物理引擎初始化完成');

                // 创建建筑
                document.getElementById('loading').innerHTML = '正在创建建筑...';
                await createBuilding();
                console.log('建筑创建完成');

                // 预编译场景
                document.getElementById('loading').innerHTML = '正在预编译场景...';
                if (renderer.compileAsync) {
                    await renderer.compileAsync(scene, camera);
                    console.log('场景预编译完成');
                }

                console.log('Three.js场景初始化完全成功');

            } catch (error) {
                console.error('Three.js初始化失败:', error);
                throw error; // 重新抛出错误以便上层处理
            }

            // 移除加载提示
            document.getElementById('loading').style.display = 'none';
            console.log('加载提示已移除，初始化完成');
        }

        // 设置灯光
        function setupLighting() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            // 主方向光（太阳光）
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -50;
            directionalLight.shadow.camera.right = 50;
            directionalLight.shadow.camera.top = 50;
            directionalLight.shadow.camera.bottom = -50;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 200;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 半球光（天空光）
            const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x545454, 0.4);
            scene.add(hemisphereLight);
        }

        // 创建地面
        function createGround() {
            const groundGeometry = new THREE.PlaneGeometry(80, 80);
            
            // 使用标准材质（WebGPU 会自动优化）
            const groundMaterial = new THREE.MeshStandardMaterial({
                color: 0x404040,
                roughness: 0.8,
                metalness: 0.2
            });
            
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            ground.static = true; // 标记为静态物体
            scene.add(ground);
        }

        // 初始化物理世界（简化版，不依赖Orillusion）
        async function initPhysics() {
            try {
                console.log('开始初始化物理引擎...');

                // 检查Ammo.js是否可用
                let ammoReady = false;
                let retryCount = 0;
                const maxRetries = 50; // 最多等待5秒

                while (!ammoReady && retryCount < maxRetries) {
                    if (typeof Ammo !== 'undefined') {
                        ammoReady = true;
                        console.log('Ammo.js已加载');
                    } else {
                        console.log(`等待Ammo.js加载... (${retryCount + 1}/${maxRetries})`);
                        await new Promise(resolve => setTimeout(resolve, 100));
                        retryCount++;
                    }
                }

                if (!ammoReady) {
                    console.warn('Ammo.js加载超时，将使用简化物理模拟');
                    // 使用简化的物理模拟
                    physics = {
                        initialized: false,
                        bodies: []
                    };
                    return;
                }

                // 尝试初始化Orillusion引擎
                try {
                    if (typeof Engine3D !== 'undefined') {
                        await Engine3D.init();
                        orillusion_scene = new Scene3D();
                        physics = orillusion_scene.addComponent(Physics);
                        await physics.init();
                        console.log('Orillusion物理引擎初始化成功');
                    } else {
                        throw new Error('Orillusion不可用');
                    }
                } catch (orillionError) {
                    console.warn('Orillusion物理引擎初始化失败，使用简化物理:', orillionError);
                    // 使用简化的物理模拟
                    physics = {
                        initialized: false,
                        bodies: []
                    };
                }

                // 创建地面物理体
                if (physics && physics.initialized !== false) {
                    createGroundPhysics();
                }

            } catch (error) {
                console.error('物理引擎初始化失败:', error);
                // 不抛出错误，使用简化物理模拟
                physics = {
                    initialized: false,
                    bodies: []
                };
                console.log('将使用简化物理模拟继续运行');
            }
        }

        // 创建地面物理体
        function createGroundPhysics() {
            // 创建地面物理对象
            const groundObject = new Object3D();
            groundObject.transform.localPosition = new Vector3(0, -0.1, 0);

            // 添加刚体组件
            const groundRigidbody = groundObject.addComponent(Rigidbody);
            groundRigidbody.mass = 0; // 静态物体
            groundRigidbody.isKinematic = false;

            // 设置碰撞形状为盒子形状
            // Orillusion会根据Object3D的几何体自动生成碰撞形状

            // 添加到Orillusion场景
            orillusion_scene.addChild(groundObject);
        }

        // 创建砖块
        function createBrick(x, y, z, rotation) {
            // Three.js网格
            const geometry = new THREE.BoxGeometry(
                BUILDING_PARAMS.brickWidth,
                BUILDING_PARAMS.brickHeight,
                BUILDING_PARAMS.brickDepth
            );

            // 使用标准材质
            const brickMaterial = new THREE.MeshStandardMaterial({
                color: 0xD2B48C, // 米色
                roughness: 0.7,
                metalness: 0.1
            });

            const mesh = new THREE.Mesh(geometry, brickMaterial);
            mesh.position.set(x, y, z);
            mesh.rotation.y = rotation;
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            mesh.static = true; // 初始标记为静态
            scene.add(mesh);

            // 物理体（兼容性处理）
            let brickObject = null;
            let rigidbody = null;

            try {
                if (physics && physics.initialized !== false && typeof Object3D !== 'undefined') {
                    // 使用Orillusion物理体
                    brickObject = new Object3D();
                    brickObject.transform.localPosition = new Vector3(x, y, z);
                    brickObject.transform.localRotation = new Quaternion().setFromAxisAngle(new Vector3(0, 1, 0), rotation);

                    // 添加刚体组件
                    rigidbody = brickObject.addComponent(Rigidbody);
                    rigidbody.mass = 5;
                    rigidbody.isKinematic = false;

                    // 设置物理材质属性
                    rigidbody.friction = 0.8;
                    rigidbody.restitution = 0.05;

                    // 添加到Orillusion场景
                    if (orillusion_scene) {
                        orillusion_scene.addChild(brickObject);
                    }
                } else {
                    // 简化物理体（仅存储位置和速度信息）
                    rigidbody = {
                        position: { x, y, z },
                        velocity: { x: 0, y: 0, z: 0 },
                        angularVelocity: { x: 0, y: 0, z: 0 },
                        mass: 5,
                        active: false
                    };
                }
            } catch (error) {
                console.warn('创建物理体失败，使用简化物理:', error);
                // 简化物理体
                rigidbody = {
                    position: { x, y, z },
                    velocity: { x: 0, y: 0, z: 0 },
                    angularVelocity: { x: 0, y: 0, z: 0 },
                    mass: 5,
                    active: false
                };
            }

            return { mesh, body: rigidbody, object3D: brickObject };
        }

        // 创建圆形建筑
        async function createBuilding() {
            building = [];
            
            for (let layer = 0; layer < BUILDING_PARAMS.layers; layer++) {
                const angleStep = (Math.PI * 2) / BUILDING_PARAMS.bricksPerLayer;
                
                for (let i = 0; i < BUILDING_PARAMS.bricksPerLayer; i++) {
                    let angle = i * angleStep;
                    
                    // 偶数层偏移50%
                    if (layer % 2 === 0) {
                        angle += angleStep * 0.5;
                    }
                    
                    // 微调半径确保紧密贴合
                    const adjustedRadius = BUILDING_PARAMS.radius - BUILDING_PARAMS.brickDepth * 0.45;
                    
                    const x = Math.cos(angle) * adjustedRadius;
                    const y = layer * BUILDING_PARAMS.brickHeight + BUILDING_PARAMS.brickHeight / 2;
                    const z = Math.sin(angle) * adjustedRadius;
                    const rotation = angle + Math.PI / 2;
                    
                    const brick = createBrick(x, y, z, rotation);
                    building.push(brick);
                }
            }
            
            // 让建筑稳定几帧
            return new Promise(resolve => {
                setTimeout(() => {
                    building.forEach(brick => {
                        brick.body.sleep();
                    });
                    resolve();
                }, 100);
            });
        }

        // 配置爆破点
        function configureBlastPoints(mode) {
            blastPoints = [];
            
            switch(mode) {
                case 'controlled':
                    setupControlledDemolition();
                    break;
                case 'sequential':
                    setupSequentialBlasting();
                    break;
                case 'simultaneous':
                    setupSimultaneousBlasting();
                    break;
                case 'implosion':
                    setupImplosion();
                    break;
            }
        }

        // 控制爆破配置
        function setupControlledDemolition() {
            // 底层关键支撑点（东侧先爆）
            for (let i = 0; i < 4; i++) {
                const angle = (Math.PI * 2 / 4) * i;
                blastPoints.push({
                    position: new THREE.Vector3(
                        Math.cos(angle) * BUILDING_PARAMS.radius,
                        1,
                        Math.sin(angle) * BUILDING_PARAMS.radius
                    ),
                    delay: i < 2 ? 0 : 100,
                    force: 800,
                    radius: 3,
                    type: 'primary'
                });
            }
            
            // 中层削弱点
            for (let layer = 10; layer < 40; layer += 10) {
                const angle = (layer / 10) * Math.PI / 2;
                blastPoints.push({
                    position: new THREE.Vector3(
                        Math.cos(angle) * BUILDING_PARAMS.radius,
                        layer,
                        Math.sin(angle) * BUILDING_PARAMS.radius
                    ),
                    delay: 200 + layer * 5,
                    force: 500,
                    radius: 2,
                    type: 'secondary'
                });
            }
            
            // 顶层引导点
            blastPoints.push({
                position: new THREE.Vector3(
                    -BUILDING_PARAMS.radius,
                    50,
                    0
                ),
                delay: 400,
                force: 300,
                radius: 2,
                type: 'guide'
            });
        }

        // 顺序爆破配置
        function setupSequentialBlasting() {
            const layersToBlast = [0, 10, 20, 30, 40, 50];
            
            layersToBlast.forEach((layer, index) => {
                for (let i = 0; i < 4; i++) {
                    const angle = (Math.PI * 2 / 4) * i;
                    blastPoints.push({
                        position: new THREE.Vector3(
                            Math.cos(angle) * BUILDING_PARAMS.radius,
                            layer + 1,
                            Math.sin(angle) * BUILDING_PARAMS.radius
                        ),
                        delay: index * 150,
                        force: 600,
                        radius: 2.5,
                        type: 'sequential'
                    });
                }
            });
        }

        // 同步爆破配置
        function setupSimultaneousBlasting() {
            // 在建筑的多个关键点同时爆破
            const positions = [
                { layer: 0, count: 8 },
                { layer: 20, count: 6 },
                { layer: 40, count: 4 }
            ];
            
            positions.forEach(pos => {
                for (let i = 0; i < pos.count; i++) {
                    const angle = (Math.PI * 2 / pos.count) * i;
                    blastPoints.push({
                        position: new THREE.Vector3(
                            Math.cos(angle) * BUILDING_PARAMS.radius,
                            pos.layer + 1,
                            Math.sin(angle) * BUILDING_PARAMS.radius
                        ),
                        delay: 0, // 同时爆破
                        force: 700,
                        radius: 3,
                        type: 'simultaneous'
                    });
                }
            });
        }

        // 内爆配置
        function setupImplosion() {
            const centerLayers = [5, 15, 25, 35];
            
            centerLayers.forEach((layer, index) => {
                for (let i = 0; i < 8; i++) {
                    const angle = (Math.PI * 2 / 8) * i;
                    const radius = BUILDING_PARAMS.radius * 0.5;
                    
                    blastPoints.push({
                        position: new THREE.Vector3(
                            Math.cos(angle) * radius,
                            layer,
                            Math.sin(angle) * radius
                        ),
                        delay: index * 50,
                        force: 600,
                        radius: 2.5,
                        type: 'implosion',
                        direction: 'inward'
                    });
                }
            });
        }

        // 预览爆破点
        function previewBlastPoints() {
            // 清除之前的预览
            document.querySelectorAll('.blast-indicator').forEach(el => el.remove());
            
            blastPoints.forEach((blast, index) => {
                // 创建爆破点标记
                const marker = new THREE.Mesh(
                    new THREE.SphereGeometry(0.5, 16, 16),
                    new THREE.MeshBasicMaterial({ 
                        color: blast.type === 'primary' ? 0xff0000 : 0xff6600,
                        transparent: true,
                        opacity: 0.6
                    })
                );
                marker.position.copy(blast.position);
                scene.add(marker);
                
                // 3秒后移除
                setTimeout(() => {
                    scene.remove(marker);
                }, 3000);
            });
        }

        // 执行爆破
        async function executeBlasting() {
            if (isExploding) return;
            isExploding = true;
            
            const delayMultiplier = parseFloat(document.getElementById('delayMultiplier').value);
            
            // 按延迟时间排序
            const sortedBlasts = [...blastPoints].sort((a, b) => a.delay - b.delay);
            
            for (const blast of sortedBlasts) {
                await delay(blast.delay * delayMultiplier);
                detonatePoint(blast);
                createBlastEffects(blast);
            }
            
            // 建筑开始倒塌后创建大规模尘埃云
            setTimeout(() => {
                createCollapseCloud();
            }, 1000);
        }

        // 延迟函数
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 单点爆破（兼容多种物理引擎）
        function detonatePoint(blast) {
            building.forEach(brick => {
                let brickPos;

                // 获取砖块位置（兼容不同的物理引擎）
                if (brick.object3D && brick.object3D.transform) {
                    // Orillusion物理引擎
                    brickPos = brick.object3D.transform.localPosition;
                } else if (brick.body && brick.body.position) {
                    // 简化物理引擎
                    brickPos = brick.body.position;
                } else {
                    // 从Three.js网格获取位置
                    brickPos = brick.mesh.position;
                }

                // 计算距离
                const dx = brickPos.x - blast.position.x;
                const dy = brickPos.y - blast.position.y;
                const dz = brickPos.z - blast.position.z;
                const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

                if (distance < blast.radius) {
                    brick.mesh.static = false; // 标记为动态物体

                    let forceX, forceY, forceZ;
                    if (blast.direction === 'inward') {
                        // 内爆
                        const length = Math.sqrt(blast.position.x * blast.position.x + blast.position.z * blast.position.z);
                        forceX = length > 0 ? (-blast.position.x / length) * blast.force : 0;
                        forceY = 0;
                        forceZ = length > 0 ? (-blast.position.z / length) * blast.force : 0;
                    } else {
                        // 外爆
                        const length = Math.sqrt(dx * dx + dz * dz);
                        const forceMagnitude = blast.force * (1 - distance / blast.radius);
                        forceX = length > 0 ? (dx / length) * forceMagnitude : 0;
                        forceY = blast.force * 0.3; // 向上的分量
                        forceZ = length > 0 ? (dz / length) * forceMagnitude : 0;
                    }

                    // 应用冲击力
                    if (brick.body && brick.body.btRigidbody && typeof Ammo !== 'undefined') {
                        // 使用Ammo.js物理引擎
                        try {
                            brick.body.btRigidbody.activate(true);
                            const ammoForce = new Ammo.btVector3(forceX, forceY, forceZ);
                            const ammoPos = new Ammo.btVector3(brickPos.x, brickPos.y, brickPos.z);
                            brick.body.btRigidbody.applyImpulse(ammoForce, ammoPos);

                            // 添加随机旋转扭矩
                            const torque = new Ammo.btVector3(
                                (Math.random() - 0.5) * blast.force * 0.1,
                                (Math.random() - 0.5) * blast.force * 0.1,
                                (Math.random() - 0.5) * blast.force * 0.1
                            );
                            brick.body.btRigidbody.applyTorque(torque);

                            // 清理临时对象
                            Ammo.destroy(ammoForce);
                            Ammo.destroy(ammoPos);
                            Ammo.destroy(torque);
                        } catch (error) {
                            console.warn('Ammo.js力应用失败:', error);
                        }
                    } else if (brick.body && brick.body.velocity !== undefined) {
                        // 使用简化物理引擎
                        brick.body.active = true;
                        brick.body.velocity.x += forceX * 0.01;
                        brick.body.velocity.y += forceY * 0.01;
                        brick.body.velocity.z += forceZ * 0.01;

                        // 添加随机旋转
                        brick.body.angularVelocity.x += (Math.random() - 0.5) * 5;
                        brick.body.angularVelocity.y += (Math.random() - 0.5) * 5;
                        brick.body.angularVelocity.z += (Math.random() - 0.5) * 5;
                    }
                }
            });
        }

        // 创建爆炸视觉效果
        function createBlastEffects(blast) {
            // 火花粒子系统
            createSparks(blast.position, blast.type);
            
            // 烟雾效果
            createSmoke(blast.position);
            
            // 冲击波
            createShockwave(blast.position, blast.radius);
            
            // 爆炸光源
            createExplosionLight(blast.position);
            
            // 混凝土碎片
            createDebris(blast.position, blast.type);
        }

        // 创建火花
        function createSparks(position, type) {
            const sparkCount = type === 'primary' ? 300 : 150;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(sparkCount * 3);
            const velocities = new Float32Array(sparkCount * 3);
            const colors = new Float32Array(sparkCount * 3);
            const lifeTimes = new Float32Array(sparkCount);
            
            for (let i = 0; i < sparkCount; i++) {
                const i3 = i * 3;
                
                // 位置
                positions[i3] = position.x;
                positions[i3 + 1] = position.y;
                positions[i3 + 2] = position.z;
                
                // 速度（随机方向）
                const speed = 10 + Math.random() * 20;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.random() * Math.PI;
                
                velocities[i3] = Math.sin(phi) * Math.cos(theta) * speed;
                velocities[i3 + 1] = Math.cos(phi) * speed;
                velocities[i3 + 2] = Math.sin(phi) * Math.sin(theta) * speed;
                
                // 颜色（黄色到橙色）
                colors[i3] = 1;
                colors[i3 + 1] = 0.5 + Math.random() * 0.5;
                colors[i3 + 2] = 0;
                
                // 生命周期
                lifeTimes[i] = 0;
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('velocity', new THREE.InstancedBufferAttribute(velocities, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            geometry.setAttribute('life', new THREE.InstancedBufferAttribute(lifeTimes, 1));
            
            // 使用标准 PointsMaterial（兼容性更好）
            const sparkMaterial = new THREE.PointsMaterial({
                size: 0.3,
                transparent: true,
                blending: THREE.AdditiveBlending,
                depthWrite: false,
                vertexColors: true
            });
            
            const sparks = new THREE.Points(geometry, sparkMaterial);
            scene.add(sparks);
            
            // 动画火花
            const startTime = Date.now();
            const animateSparks = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 2) {
                    scene.remove(sparks);
                    return;
                }
                
                const positions = sparks.geometry.attributes.position.array;
                const velocities = sparks.geometry.attributes.velocity.array;
                const lifeTimes = sparks.geometry.attributes.life.array;
                
                for (let i = 0; i < sparkCount; i++) {
                    const i3 = i * 3;
                    
                    // 更新位置
                    positions[i3] += velocities[i3] * 0.016;
                    positions[i3 + 1] += velocities[i3 + 1] * 0.016 - 9.8 * 0.016 * elapsed;
                    positions[i3 + 2] += velocities[i3 + 2] * 0.016;
                    
                    // 更新生命周期
                    lifeTimes[i] = elapsed;
                }
                
                sparks.geometry.attributes.position.needsUpdate = true;
                sparks.geometry.attributes.life.needsUpdate = true;
                
                requestAnimationFrame(animateSparks);
            };
            
            animateSparks();
        }

        // 创建烟雾
        function createSmoke(position) {
            const smokeCount = 50;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(smokeCount * 3);
            const sizes = new Float32Array(smokeCount);
            const randoms = new Float32Array(smokeCount);
            
            for (let i = 0; i < smokeCount; i++) {
                const i3 = i * 3;
                
                positions[i3] = position.x + (Math.random() - 0.5) * 2;
                positions[i3 + 1] = position.y;
                positions[i3 + 2] = position.z + (Math.random() - 0.5) * 2;
                
                sizes[i] = 2 + Math.random() * 3;
                randoms[i] = Math.random();
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('size', new THREE.InstancedBufferAttribute(sizes, 1));
            geometry.setAttribute('random', new THREE.InstancedBufferAttribute(randoms, 1));
            
            // 使用标准 PointsMaterial
            const smokeMaterial = new THREE.PointsMaterial({
                size: 3,
                transparent: true,
                depthWrite: false,
                color: 0x333333,
                opacity: 0.6
            });
            
            const smoke = new THREE.Points(geometry, smokeMaterial);
            scene.add(smoke);
            
            // 动画烟雾
            const startTime = Date.now();
            const animateSmoke = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 10) {
                    scene.remove(smoke);
                    return;
                }
                
                const positions = smoke.geometry.attributes.position.array;
                const randoms = smoke.geometry.attributes.random.array;
                
                for (let i = 0; i < smokeCount; i++) {
                    const i3 = i * 3;
                    
                    // 烟雾上升和扩散（使用随机值产生不同的运动）
                    positions[i3] += (Math.sin(elapsed * 3 + randoms[i] * 6.28) * 0.05);
                    positions[i3 + 1] += 0.05 + randoms[i] * 0.05;
                    positions[i3 + 2] += (Math.cos(elapsed * 3 + randoms[i] * 6.28) * 0.05);
                }
                
                smoke.geometry.attributes.position.needsUpdate = true;

                // 更新烟雾大小和透明度
                smoke.material.size = 3 * (1 + elapsed * 2);
                smoke.material.opacity = 0.6 / (1 + elapsed * 2);
                
                requestAnimationFrame(animateSmoke);
            };
            
            animateSmoke();
        }

        // 创建冲击波
        function createShockwave(position, radius) {
            const geometry = new THREE.RingGeometry(0.1, radius * 2, 32);

            // 使用标准材质
            const shockwaveMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.5,
                side: THREE.DoubleSide
            });

            const shockwave = new THREE.Mesh(geometry, shockwaveMaterial);
            shockwave.position.copy(position);
            shockwave.rotation.x = -Math.PI / 2;
            scene.add(shockwave);

            // 动画冲击波
            const startTime = Date.now();
            const animateShockwave = () => {
                const elapsed = (Date.now() - startTime) / 1000;

                if (elapsed > 1) {
                    scene.remove(shockwave);
                    return;
                }

                const scale = 1 + elapsed * 10;
                shockwave.scale.set(scale, scale, scale);
                shockwave.material.opacity = 0.5 * (1 - elapsed);

                requestAnimationFrame(animateShockwave);
            };

            animateShockwave();
        }

        // 创建爆炸光源
        function createExplosionLight(position) {
            const light = new THREE.PointLight(0xffaa00, 10, 20);
            light.position.copy(position);
            scene.add(light);
            
            // 光源衰减动画
            const startTime = Date.now();
            const animateLight = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 0.5) {
                    scene.remove(light);
                    return;
                }
                
                light.intensity = 10 * (1 - elapsed * 2);
                
                requestAnimationFrame(animateLight);
            };
            
            animateLight();
        }

        // 创建混凝土碎片
        function createDebris(position, type) {
            const debrisCount = type === 'primary' ? 30 : 15;
            const debrisGroup = new THREE.Group();
            
            for (let i = 0; i < debrisCount; i++) {
                // 创建不规则碎片几何体
                const size = 0.1 + Math.random() * 0.3;
                const geometry = new THREE.TetrahedronGeometry(size, 0);
                
                // 随机变形
                const positions = geometry.attributes.position.array;
                for (let j = 0; j < positions.length; j += 3) {
                    positions[j] += (Math.random() - 0.5) * size * 0.3;
                    positions[j + 1] += (Math.random() - 0.5) * size * 0.3;
                    positions[j + 2] += (Math.random() - 0.5) * size * 0.3;
                }
                geometry.attributes.position.needsUpdate = true;
                
                const material = new THREE.MeshStandardMaterial({
                    color: 0x8B7355, // 深褐色
                    roughness: 0.9,
                    metalness: 0
                });
                
                const debris = new THREE.Mesh(geometry, material);
                debris.position.copy(position);
                debris.position.add(new THREE.Vector3(
                    (Math.random() - 0.5) * 2,
                    Math.random(),
                    (Math.random() - 0.5) * 2
                ));
                
                // 初始速度
                debris.userData.velocity = new THREE.Vector3(
                    (Math.random() - 0.5) * 10,
                    Math.random() * 15,
                    (Math.random() - 0.5) * 10
                );
                
                // 初始旋转速度
                debris.userData.angularVelocity = new THREE.Vector3(
                    Math.random() * 10,
                    Math.random() * 10,
                    Math.random() * 10
                );
                
                debris.castShadow = true;
                debrisGroup.add(debris);
            }
            
            scene.add(debrisGroup);
            
            // 动画碎片
            const startTime = Date.now();
            const animateDebris = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 5) {
                    scene.remove(debrisGroup);
                    return;
                }
                
                debrisGroup.children.forEach(debris => {
                    // 更新位置
                    debris.position.add(debris.userData.velocity.clone().multiplyScalar(0.016));
                    
                    // 重力
                    debris.userData.velocity.y -= 9.8 * 0.016;
                    
                    // 旋转
                    debris.rotation.x += debris.userData.angularVelocity.x * 0.016;
                    debris.rotation.y += debris.userData.angularVelocity.y * 0.016;
                    debris.rotation.z += debris.userData.angularVelocity.z * 0.016;
                    
                    // 地面碰撞
                    if (debris.position.y < 0.1) {
                        debris.position.y = 0.1;
                        debris.userData.velocity.y *= -0.3; // 弹性碰撞
                        debris.userData.velocity.x *= 0.8; // 摩擦
                        debris.userData.velocity.z *= 0.8;
                        
                        // 碰撞时产生小尘土
                        if (Math.abs(debris.userData.velocity.y) > 1) {
                            createDustPuff(debris.position);
                        }
                    }
                });
                
                requestAnimationFrame(animateDebris);
            };
            
            animateDebris();
        }

        // 创建尘土飞扬效果
        function createDustPuff(position) {
            const dustCount = 10;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(dustCount * 3);
            const sizes = new Float32Array(dustCount);
            
            for (let i = 0; i < dustCount; i++) {
                const i3 = i * 3;
                
                positions[i3] = position.x + (Math.random() - 0.5) * 0.5;
                positions[i3 + 1] = 0.1;
                positions[i3 + 2] = position.z + (Math.random() - 0.5) * 0.5;
                
                sizes[i] = 0.5 + Math.random() * 0.5;
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
            
            const material = new THREE.PointsMaterial({
                size: 1,
                color: 0x8B7D6B, // 尘土色
                transparent: true,
                opacity: 0.4,
                depthWrite: false
            });
            
            const dust = new THREE.Points(geometry, material);
            scene.add(dust);
            
            // 动画尘土
            const startTime = Date.now();
            const animateDust = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 2) {
                    scene.remove(dust);
                    return;
                }
                
                const positions = dust.geometry.attributes.position.array;
                
                for (let i = 0; i < dustCount; i++) {
                    const i3 = i * 3;
                    
                    // 尘土上升和扩散
                    positions[i3] += (Math.random() - 0.5) * 0.02;
                    positions[i3 + 1] += 0.01;
                    positions[i3 + 2] += (Math.random() - 0.5) * 0.02;
                }
                
                dust.geometry.attributes.position.needsUpdate = true;
                dust.material.opacity = 0.4 * (1 - elapsed / 2);
                dust.material.size = 1 + elapsed;
                
                requestAnimationFrame(animateDust);
            };
            
            animateDust();
        }

        // 创建大规模倒塌尘埃云
        function createCollapseCloud() {
            const cloudParticles = 500;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(cloudParticles * 3);
            const sizes = new Float32Array(cloudParticles);
            const velocities = new Float32Array(cloudParticles * 3);
            const randoms = new Float32Array(cloudParticles);
            
            for (let i = 0; i < cloudParticles; i++) {
                const i3 = i * 3;
                
                // 从建筑底部开始
                const angle = Math.random() * Math.PI * 2;
                const radius = BUILDING_PARAMS.radius + Math.random() * 5;
                
                positions[i3] = Math.cos(angle) * radius;
                positions[i3 + 1] = Math.random() * 5;
                positions[i3 + 2] = Math.sin(angle) * radius;
                
                // 向外扩散的速度
                velocities[i3] = Math.cos(angle) * (2 + Math.random() * 3);
                velocities[i3 + 1] = Math.random() * 2;
                velocities[i3 + 2] = Math.sin(angle) * (2 + Math.random() * 3);
                
                sizes[i] = 5 + Math.random() * 10;
                randoms[i] = Math.random();
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('size', new THREE.InstancedBufferAttribute(sizes, 1));
            geometry.setAttribute('velocity', new THREE.InstancedBufferAttribute(velocities, 3));
            geometry.setAttribute('random', new THREE.InstancedBufferAttribute(randoms, 1));
            
            // 使用标准 PointsMaterial
            const cloudMaterial = new THREE.PointsMaterial({
                size: 8,
                transparent: true,
                depthWrite: false,
                blending: THREE.NormalBlending,
                color: 0x666666,
                opacity: 0.3
            });
            
            const cloud = new THREE.Points(geometry, cloudMaterial);
            scene.add(cloud);
            
            // 动画尘埃云
            const startTime = Date.now();
            const animateCloud = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 15) {
                    scene.remove(cloud);
                    return;
                }
                
                const positions = cloud.geometry.attributes.position.array;
                const velocities = cloud.geometry.attributes.velocity.array;
                
                for (let i = 0; i < cloudParticles; i++) {
                    const i3 = i * 3;
                    
                    // 更新位置
                    positions[i3] += velocities[i3] * 0.016;
                    positions[i3 + 1] += velocities[i3 + 1] * 0.016;
                    positions[i3 + 2] += velocities[i3 + 2] * 0.016;
                    
                    // 速度衰减
                    velocities[i3] *= 0.99;
                    velocities[i3 + 1] *= 0.99;
                    velocities[i3 + 2] *= 0.99;
                }
                
                cloud.geometry.attributes.position.needsUpdate = true;
                cloud.geometry.attributes.velocity.needsUpdate = true;

                // 更新尘埃云大小和透明度
                cloud.material.size = 8 * (1 + elapsed * 0.5);
                cloud.material.opacity = 0.3 / Math.sqrt(1 + elapsed * 0.5);
                
                requestAnimationFrame(animateCloud);
            };
            
            animateCloud();
        }

        // 重置场景
        async function resetScene() {
            try {
                console.log('开始重置场景...');

                // 停止爆炸
                isExploding = false;

                // 清除旧建筑
                building.forEach(brick => {
                    scene.remove(brick.mesh);

                    // 清除物理体（兼容处理）
                    if (brick.object3D && orillusion_scene && orillusion_scene.removeChild) {
                        try {
                            orillusion_scene.removeChild(brick.object3D);
                        } catch (error) {
                            console.warn('移除Orillusion物理体失败:', error);
                        }
                    }
                });
                building = [];

                // 清除粒子系统
                particleSystems.forEach(system => {
                    scene.remove(system);
                });
                particleSystems = [];

                // 重新创建建筑
                await createBuilding();

                console.log('场景重置完成');
            } catch (error) {
                console.error('重置场景失败:', error);
            }
        }

        // 动画循环
        function animate() {
            try {
                // 更新物理世界
                if (physics && physics.initialized !== false) {
                    // 使用Orillusion物理引擎
                    if (orillusion_scene && orillusion_scene.update) {
                        orillusion_scene.update();
                    }

                    // 同步Orillusion物理和Three.js渲染
                    building.forEach(brick => {
                        if (brick.object3D && brick.body && brick.body.btRigidbody) {
                            // 从Orillusion物理对象同步位置和旋转到Three.js网格
                            const pos = brick.object3D.transform.localPosition;
                            const rot = brick.object3D.transform.localRotation;

                            brick.mesh.position.set(pos.x, pos.y, pos.z);
                            brick.mesh.quaternion.set(rot.x, rot.y, rot.z, rot.w);
                        }
                    });
                } else {
                    // 使用简化物理模拟
                    building.forEach(brick => {
                        if (brick.body && brick.body.active) {
                            // 简单的重力和运动模拟
                            brick.body.velocity.y -= 0.5; // 重力

                            // 更新位置
                            brick.body.position.x += brick.body.velocity.x * 0.016;
                            brick.body.position.y += brick.body.velocity.y * 0.016;
                            brick.body.position.z += brick.body.velocity.z * 0.016;

                            // 地面碰撞检测
                            if (brick.body.position.y < 0.5) {
                                brick.body.position.y = 0.5;
                                brick.body.velocity.y *= -0.3; // 弹性碰撞
                                brick.body.velocity.x *= 0.8; // 摩擦
                                brick.body.velocity.z *= 0.8;
                            }

                            // 更新Three.js网格位置
                            brick.mesh.position.set(
                                brick.body.position.x,
                                brick.body.position.y,
                                brick.body.position.z
                            );

                            // 简单的旋转动画
                            brick.mesh.rotation.x += brick.body.angularVelocity.x * 0.016;
                            brick.mesh.rotation.y += brick.body.angularVelocity.y * 0.016;
                            brick.mesh.rotation.z += brick.body.angularVelocity.z * 0.016;
                        }
                    });
                }

                // 更新控制器
                controls.update();

                // 渲染场景
                if (renderer.renderAsync) {
                    renderer.renderAsync(scene, camera);
                } else {
                    renderer.render(scene, camera);
                }
            } catch (error) {
                console.error('动画循环错误:', error);
            }
        }

        // 设置UI事件
        function setupUI() {
            // 爆破模式选择
            const blastModeSelect = document.getElementById('blastMode');
            blastModeSelect.addEventListener('change', (e) => {
                configureBlastPoints(e.target.value);
            });
            
            // 延迟倍率滑块
            const delaySlider = document.getElementById('delayMultiplier');
            const delayValue = document.querySelector('.slider-value');
            delaySlider.addEventListener('input', (e) => {
                delayValue.textContent = e.target.value + 'x';
            });
            
            // 预览爆破点按钮
            document.getElementById('previewBtn').addEventListener('click', () => {
                configureBlastPoints(blastModeSelect.value);
                previewBlastPoints();
            });
            
            // 重置按钮
            document.getElementById('resetBtn').addEventListener('click', async () => {
                await resetScene();
                document.getElementById('detonateBtn').disabled = false;
            });
            
            // 爆破按钮
            document.getElementById('detonateBtn').addEventListener('click', () => {
                if (!isExploding) {
                    configureBlastPoints(blastModeSelect.value);
                    executeBlasting();
                    document.getElementById('detonateBtn').disabled = true;
                }
            });
            
            // 相机视角按钮
            document.getElementById('cameraView1').addEventListener('click', () => {
                camera.position.set(40, 30, 40);
                controls.target.set(0, 20, 0);
                controls.update();
            });
            
            document.getElementById('cameraView2').addEventListener('click', () => {
                camera.position.set(0, 80, 0);
                controls.target.set(0, 20, 0);
                controls.update();
            });
            
            document.getElementById('cameraView3').addEventListener('click', () => {
                let angle = 0;
                const radius = 50;
                const rotateCamera = () => {
                    angle += 0.01;
                    camera.position.x = Math.cos(angle) * radius;
                    camera.position.z = Math.sin(angle) * radius;
                    camera.position.y = 30;
                    camera.lookAt(0, 20, 0);
                    
                    if (angle < Math.PI * 2) {
                        requestAnimationFrame(rotateCamera);
                    }
                };
                rotateCamera();
            });
        }

        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // 初始化
        async function init() {
            await initThree();
            setupUI();

            // 设置默认爆破模式
            configureBlastPoints('controlled');

            // 设置异步动画循环
            renderer.setAnimationLoop(animate);

            // 监听窗口大小变化
            window.addEventListener('resize', onWindowResize);
        }

        // 启动应用
        init().catch(error => {
            console.error('应用启动失败:', error);
            document.getElementById('loading').innerHTML = '加载失败: ' + error.message;
            // 显示详细错误信息
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = 'position: absolute; top: 60%; left: 50%; transform: translate(-50%, -50%); color: red; background: rgba(0,0,0,0.8); padding: 20px; border-radius: 10px; max-width: 80%; font-size: 14px; white-space: pre-wrap;';
            errorDiv.textContent = error.stack || error.message;
            document.body.appendChild(errorDiv);
        });
    </script>
</body>
</html>