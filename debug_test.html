<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
        }
        
        #log {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #666;
            padding-left: 10px;
        }
        
        .log-success {
            border-left-color: #4CAF50;
            color: #4CAF50;
        }
        
        .log-error {
            border-left-color: #f44336;
            color: #f44336;
        }
        
        .log-warning {
            border-left-color: #ff9800;
            color: #ff9800;
        }
        
        .log-info {
            border-left-color: #2196F3;
            color: #2196F3;
        }
        
        #status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #666;
        }
        
        .status-loading {
            border-color: #ff9800;
            color: #ff9800;
        }
        
        .status-success {
            border-color: #4CAF50;
            color: #4CAF50;
        }
        
        .status-error {
            border-color: #f44336;
            color: #f44336;
        }
    </style>
</head>
<body>
    <h1>🔍 Three.js 加载调试测试</h1>
    <div id="status" class="status-loading">正在检测...</div>
    
    <div id="log">
        <div class="log-entry log-info">开始调试测试...</div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.169.0/build/three.webgpu.js",
            "three/tsl": "https://unpkg.com/three@0.169.0/build/three.webgpu.js",
            "three/addons/": "https://unpkg.com/three@0.169.0/examples/jsm/",
            "@orillusion/core": "https://unpkg.com/@orillusion/core@latest/dist/orillusion.es.js",
            "@orillusion/physics": "https://unpkg.com/@orillusion/physics@latest/dist/physics.es.js"
        }
    }
    </script>

    <script src="https://unpkg.com/ammo.js@0.0.10/builds/ammo.js"></script>

    <script type="module">
        const logContainer = document.getElementById('log');
        const statusDiv = document.getElementById('status');
        
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(message, type = 'loading') {
            statusDiv.textContent = message;
            statusDiv.className = `status-${type}`;
        }
        
        async function testModuleLoading() {
            log('开始测试模块加载...', 'info');
            updateStatus('测试中...', 'loading');
            
            // 测试 Three.js
            try {
                log('尝试加载 Three.js...', 'info');
                const threeModule = await import('three');
                log('Three.js 加载成功！', 'success');
                log(`Three.js 版本: ${threeModule.REVISION}`, 'info');
                
                // 测试 OrbitControls
                try {
                    log('尝试加载 OrbitControls...', 'info');
                    const controlsModule = await import('three/addons/controls/OrbitControls.js');
                    log('OrbitControls 加载成功！', 'success');
                } catch (error) {
                    log(`OrbitControls 加载失败: ${error.message}`, 'error');
                }
                
                // 测试 WebGPU
                try {
                    log('尝试加载 WebGPU 模块...', 'info');
                    const webgpuModule = await import('three/addons/capabilities/WebGPU.js');
                    log('WebGPU 模块加载成功！', 'success');
                    
                    const isAvailable = webgpuModule.default.isAvailable();
                    log(`WebGPU 可用性: ${isAvailable}`, isAvailable ? 'success' : 'warning');
                } catch (error) {
                    log(`WebGPU 模块加载失败: ${error.message}`, 'error');
                }
                
            } catch (error) {
                log(`Three.js 加载失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
                updateStatus('Three.js 加载失败', 'error');
                return;
            }
            
            // 测试 Orillusion
            try {
                log('尝试加载 Orillusion 核心模块...', 'info');
                const orillionCoreModule = await import('@orillusion/core');
                log('Orillusion 核心模块加载成功！', 'success');
                
                try {
                    log('尝试加载 Orillusion 物理模块...', 'info');
                    const orillionPhysicsModule = await import('@orillusion/physics');
                    log('Orillusion 物理模块加载成功！', 'success');
                } catch (error) {
                    log(`Orillusion 物理模块加载失败: ${error.message}`, 'warning');
                }
                
            } catch (error) {
                log(`Orillusion 模块加载失败: ${error.message}`, 'warning');
                log('这是正常的，将使用简化物理模拟', 'info');
            }
            
            // 测试 Ammo.js
            let ammoReady = false;
            let retryCount = 0;
            const maxRetries = 30;
            
            log('检测 Ammo.js 加载状态...', 'info');
            while (!ammoReady && retryCount < maxRetries) {
                if (typeof Ammo !== 'undefined') {
                    ammoReady = true;
                    log('Ammo.js 已加载！', 'success');
                } else {
                    retryCount++;
                    if (retryCount % 10 === 0) {
                        log(`等待 Ammo.js 加载... (${retryCount}/${maxRetries})`, 'info');
                    }
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
            
            if (!ammoReady) {
                log('Ammo.js 加载超时', 'warning');
            }
            
            // 测试基本 Three.js 功能
            try {
                log('测试 Three.js 基本功能...', 'info');
                const threeModule = await import('three');
                const THREE = threeModule;
                
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
                
                // 尝试创建 WebGPU 渲染器
                try {
                    const renderer = new THREE.WebGPURenderer();
                    log('WebGPU 渲染器创建成功！', 'success');
                    await renderer.init();
                    log('WebGPU 渲染器初始化成功！', 'success');
                } catch (webgpuError) {
                    log(`WebGPU 渲染器失败: ${webgpuError.message}`, 'warning');
                    
                    // 尝试 WebGL 渲染器
                    try {
                        const renderer = new THREE.WebGLRenderer();
                        log('WebGL 渲染器创建成功！', 'success');
                    } catch (webglError) {
                        log(`WebGL 渲染器也失败: ${webglError.message}`, 'error');
                        throw webglError;
                    }
                }
                
                log('Three.js 基本功能测试通过！', 'success');
                
            } catch (error) {
                log(`Three.js 功能测试失败: ${error.message}`, 'error');
                updateStatus('功能测试失败', 'error');
                return;
            }
            
            log('所有测试完成！', 'success');
            updateStatus('测试完成', 'success');
        }
        
        // 开始测试
        testModuleLoading().catch(error => {
            log(`测试过程中发生未捕获的错误: ${error.message}`, 'error');
            log(`错误堆栈: ${error.stack}`, 'error');
            updateStatus('测试失败', 'error');
        });
    </script>
</body>
</html>
